import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ResizablePanel, ResizableHandle, ResizablePanelGroup } from '@/components/ui/resizable';
import { ScrollArea } from '@/components/ui/scroll-area';
import { supabase } from '@/integrations/supabase/client';
import { BlogPost } from '@/types/database';
import { toast } from '@/hooks/use-toast';
import RichTextEditor from './RichTextEditor';
import BlogFormSEOPanel from './BlogFormSEOPanel';
import SchemaPreviewPanel from './SchemaPreviewPanel';
import BlogPreview from '@/components/BlogPreview';

interface BlogFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  blog?: BlogPost | null;
}

const BlogFormModal: React.FC<BlogFormModalProps> = ({ isOpen, onClose, blog }) => {
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    content: '',
    excerpt: '',
    cover_image: '',
    tags: '',
    published: true
  });
  const [loading, setLoading] = useState(false);
  const [autosaving, setAutosaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  useEffect(() => {
    if (blog) {
      setFormData({
        title: blog.title,
        slug: blog.slug,
        content: blog.content,
        excerpt: blog.excerpt,
        cover_image: blog.cover_image || '',
        tags: blog.tags.join(', '),
        published: blog.published
      });
    } else {
      setFormData({
        title: '',
        slug: '',
        content: '',
        excerpt: '',
        cover_image: '',
        tags: '',
        published: true
      });
    }
  }, [blog, isOpen]);

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    setFormData(prev => ({
      ...prev,
      title,
      slug: generateSlug(title)
    }));
  };

  // Handle autosaving
  const saveDraft = useCallback(async () => {
    if (!blog?.id) return;

    try {
      setAutosaving(true);
      const { error } = await supabase
        .from('blog_drafts')
        .upsert({
          blog_id: blog.id,
          user_id: (await supabase.auth.getUser()).data.user?.id,
          title: formData.title,
          content: formData.content,
          excerpt: formData.excerpt,
          cover_image: formData.cover_image,
          tags: formData.tags.split(',').map(t => t.trim()).filter(t => t),
          last_saved_at: new Date().toISOString()
        });

      if (error) throw error;
      setLastSaved(new Date());
    } catch (error) {
      console.error('Error saving draft:', error);
    } finally {
      setAutosaving(false);
    }
  }, [formData, blog?.id]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const tagsArray = formData.tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      const blogData = {
        title: formData.title,
        slug: formData.slug,
        content: formData.content,
        excerpt: formData.excerpt,
        cover_image: formData.cover_image || null,
        tags: tagsArray,
        published: formData.published
      };

      if (blog) {
        const { error } = await supabase
          .from('blog_posts')
          .update(blogData)
          .eq('id', blog.id);

        if (error) throw error;

        toast({
          title: "Success",
          description: "Blog post updated successfully",
        });
      } else {
        const { error } = await supabase
          .from('blog_posts')
          .insert([blogData]);

        if (error) throw error;

        toast({
          title: "Success",
          description: "Blog post created successfully",
        });
      }

      onClose();
    } catch (error) {
      console.error('Error saving blog:', error);
      toast({
        title: "Error",
        description: "Failed to save blog post",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {blog ? 'Edit Blog Post' : 'Add New Blog Post'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="col-span-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={handleTitleChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                required
              />
            </div>
          </div>

          <Tabs defaultValue="editor" className="flex-1">
            <TabsList>
              <TabsTrigger value="editor">Editor</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
              <TabsTrigger value="schema">Schema</TabsTrigger>
            </TabsList>
            
            <TabsContent value="editor" className="space-y-4">
              <ResizablePanelGroup direction="horizontal">
                <ResizablePanel defaultSize={65} minSize={50} maxSize={80}>
                  <div className="h-[600px]">
                    <RichTextEditor
                      value={formData.content}
                      onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                      onAutosave={saveDraft}
                      placeholder="Write your blog post content here..."
                    />
                  </div>
                </ResizablePanel>
                
                <ResizableHandle withHandle />
                
                <ResizablePanel defaultSize={35} minSize={20} maxSize={50}>
                  <ScrollArea className="h-[600px]">
                    <BlogFormSEOPanel
                      content={formData.content}
                      title={formData.title}
                      excerpt={formData.excerpt}
                      tags={formData.tags.split(',').map(t => t.trim()).filter(t => t)}
                      blogId={blog?.id}
                      onSuggestionApply={(field, value) => {
                        switch (field) {
                          case 'title':
                            setFormData(prev => ({ ...prev, title: value }));
                            break;
                          case 'meta':
                            setFormData(prev => ({ ...prev, excerpt: value }));
                            break;
                          case 'keyword':
                            setFormData(prev => ({
                              ...prev,
                              tags: value.includes(',') ? value : `${prev.tags}, ${value}`
                            }));
                            break;
                        }
                      }}
                    />
                  </ScrollArea>
                </ResizablePanel>
              </ResizablePanelGroup>
            </TabsContent>

            <TabsContent value="preview" className="border rounded-lg min-h-[600px]">
              <BlogPreview content={formData.content} className="prose dark:prose-invert max-w-none p-4" />
            </TabsContent>

            <TabsContent value="schema" className="border rounded-lg min-h-[600px]">
              <SchemaPreviewPanel
                title={formData.title}
                content={formData.content}
                excerpt={formData.excerpt}
                tags={formData.tags.split(',').map(t => t.trim()).filter(t => t)}
                coverImage={formData.cover_image}
                slug={formData.slug}
                blogId={blog?.id}
              />
            </TabsContent>
          </Tabs>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="excerpt">Excerpt</Label>
              <Input
                id="excerpt"
                value={formData.excerpt}
                onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                required
              />
            </div>
            <div>
              <Label htmlFor="cover_image">Cover Image URL</Label>
              <Input
                id="cover_image"
                value={formData.cover_image}
                onChange={(e) => setFormData(prev => ({ ...prev, cover_image: e.target.value }))}
                placeholder="https://example.com/image.jpg"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="tags">Tags (comma-separated)</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
              placeholder="tag1, tag2, tag3"
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                id="published"
                checked={formData.published}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, published: checked }))}
              />
              <Label htmlFor="published">Published</Label>
            </div>

            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground">
                {autosaving && "Saving draft..."}
                {lastSaved && !autosaving && `Last saved ${new Date(lastSaved).toLocaleTimeString()}`}
              </div>
              
              <div className="flex space-x-2">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Saving...' : (blog ? 'Update' : 'Create')}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default BlogFormModal;
