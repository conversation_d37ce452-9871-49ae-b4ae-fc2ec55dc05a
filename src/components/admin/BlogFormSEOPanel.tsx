import React, { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { supabase } from '@/integrations/supabase/client';
import { Info, Check, AlertTriangle } from 'lucide-react';

interface SEOSuggestion {
  type: 'title' | 'meta' | 'keyword';
  suggestion: string;
  reason: string;
  impact: 'high' | 'medium' | 'low';
}

interface BlogFormSEOPanelProps {
  content: string;
  title: string;
  excerpt: string;
  tags: string[];
  blogId?: string;
  onSuggestionApply: (field: 'title' | 'meta' | 'keyword', value: string) => void;
}

export default function BlogFormSEOPanel({
  content,
  title,
  excerpt,
  tags,
  blogId,
  onSuggestionApply,
}: BlogFormSEOPanelProps) {
  const [suggestions, setSuggestions] = useState<SEOSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [score, setScore] = useState<number>(0);

  useEffect(() => {
    const analyzeContent = async () => {
      if (!content || !title) return;
      
      setLoading(true);
      try {
        const { data, error } = await supabase.functions.invoke('seo-optimizer', {
          body: {
            content,
            title,
            excerpt,
            tags,
            blogId,
          },
        });

        if (error) throw error;

        setSuggestions(data.suggestions);
        setScore(data.score);
      } catch (error) {
        console.error('Error analyzing content:', error);
      } finally {
        setLoading(false);
      }
    };

    // Debounce the analysis to avoid too many API calls
    const timeoutId = setTimeout(analyzeContent, 1000);
    return () => clearTimeout(timeoutId);
  }, [content, title, excerpt, tags, blogId]);

  const getImpactColor = (impact: 'high' | 'medium' | 'low') => {
    switch (impact) {
      case 'high':
        return 'bg-destructive text-destructive-foreground';
      case 'medium':
        return 'bg-yellow-500 text-yellow-50';
      case 'low':
        return 'bg-green-500 text-green-50';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  if (loading) {
    return (
      <div className="space-y-4 p-4">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-24 w-full" />
        <Skeleton className="h-24 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">SEO Analysis</h3>
        <Badge variant={score >= 80 ? 'default' : score >= 60 ? 'secondary' : 'destructive'}>
          Score: {score}
        </Badge>
      </div>

      <ScrollArea className="h-[500px] rounded-md border p-4">
        {suggestions.length === 0 ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4" />
              <span>No suggestions available</span>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {suggestions.map((suggestion, index) => (
              <Card key={index} className="p-4">
                <div className="flex items-start gap-4">
                  <div className="shrink-0">
                    {suggestion.impact === 'high' ? (
                      <AlertTriangle className="h-5 w-5 text-destructive" />
                    ) : (
                      <Info className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <Badge className={getImpactColor(suggestion.impact)}>
                        {suggestion.impact.toUpperCase()} PRIORITY
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onSuggestionApply(suggestion.type, suggestion.suggestion)}
                      >
                        Apply
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground">{suggestion.reason}</p>
                    <p className="text-sm font-medium">{suggestion.suggestion}</p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
