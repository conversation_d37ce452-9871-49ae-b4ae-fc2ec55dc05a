import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Mountain, LogOut, Package, FileText, MessageSquare, Users } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import AdminTreks from '@/components/admin/AdminTreks';
import AdminBlogs from '@/components/admin/AdminBlogs';
import AdminBookings from '@/components/admin/AdminBookings';
import AdminTestimonials from '@/components/admin/AdminTestimonials';
import AdminSeoLogs from '@/components/admin/AdminSeoLogs';
import ContentCalendar from '@/components/admin/ContentCalendar';
import SchemaManager from '@/components/admin/SchemaManager';
import ContentFreshnessMonitor from '@/components/admin/ContentFreshnessMonitor';

const AdminDashboard = () => {
  interface AdminUser {
    id: string;
    email?: string;
    user_metadata?: Record<string, unknown>;
    app_metadata?: Record<string, unknown>;
    aud?: string;
    created_at?: string;
    role?: string;
  }

  const [user, setUser] = useState<AdminUser | null>(null);
  const [stats, setStats] = useState({
    treks: 0,
    blogs: 0,
    bookings: 0,
    testimonials: 0
  });
  const navigate = useNavigate();

  useEffect(() => {
    checkAuth();
    fetchStats();
  }, []);

  const checkAuth = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      navigate('/admin/login');
      return;
    }

    const { data: adminData } = await supabase
      .from('admin_users')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (!adminData) {
      navigate('/admin/login');
      return;
    }

    setUser(user);
  };

  const fetchStats = async () => {
    try {
      const [treksRes, blogsRes, bookingsRes, testimonialsRes] = await Promise.all([
        supabase.from('trek_packages').select('id', { count: 'exact' }),
        supabase.from('blog_posts').select('id', { count: 'exact' }),
        supabase.from('booking_inquiries').select('id', { count: 'exact' }),
        supabase.from('testimonials').select('id', { count: 'exact' })
      ]);

      setStats({
        treks: treksRes.count || 0,
        blogs: blogsRes.count || 0,
        bookings: bookingsRes.count || 0,
        testimonials: testimonialsRes.count || 0
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleLogout = async () => {
    await supabase.auth.signOut();
    toast({
      title: "Logged out successfully",
      description: "You have been logged out of the admin panel.",
    });
    navigate('/admin/login');
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Mountain className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">TrekNepalX Admin</h1>
                <p className="text-sm text-gray-600">Content Management System</p>
              </div>
            </div>
            <Button onClick={handleLogout} variant="outline" className="flex items-center">
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* SEO Stats */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">SEO Performance</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Content Freshness</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">87%</div>
                <p className="text-xs text-muted-foreground">Last 30 days</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Schema Coverage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">92%</div>
                <p className="text-xs text-muted-foreground">Pages with valid schemas</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Content Updates Needed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">12</div>
                <p className="text-xs text-muted-foreground">High priority updates</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Content Stats */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Content Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Trek Packages</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.treks}</div>
              <p className="text-xs text-muted-foreground">Active packages</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Blog Posts</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.blogs}</div>
              <p className="text-xs text-muted-foreground">Published articles</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Booking Inquiries</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.bookings}</div>
              <p className="text-xs text-muted-foreground">Total inquiries</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Testimonials</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.testimonials}</div>
              <p className="text-xs text-muted-foreground">Customer reviews</p>
            </CardContent>
          </Card>
          </div>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="treks" className="space-y-4">
          <TabsList className="grid w-full grid-cols-9">
            <TabsTrigger value="treks">Trek Packages</TabsTrigger>
            <TabsTrigger value="blogs">Blog Posts</TabsTrigger>
            <TabsTrigger value="bookings">Bookings</TabsTrigger>
            <TabsTrigger value="testimonials">Testimonials</TabsTrigger>
            <TabsTrigger value="seo-logs">SEO Logs</TabsTrigger>
            <TabsTrigger value="content-calendar">Content Calendar</TabsTrigger>
            <TabsTrigger value="schemas">Schema Manager</TabsTrigger>
            <TabsTrigger value="freshness">Content Freshness</TabsTrigger>
          </TabsList>

          <TabsContent value="treks">
            <AdminTreks onStatsChange={fetchStats} />
          </TabsContent>

          <TabsContent value="blogs">
            <AdminBlogs onStatsChange={fetchStats} />
          </TabsContent>

          <TabsContent value="bookings">
            <AdminBookings onStatsChange={fetchStats} />
          </TabsContent>

          <TabsContent value="testimonials">
            <AdminTestimonials onStatsChange={fetchStats} />
          </TabsContent>

          <TabsContent value="seo-logs">
            <AdminSeoLogs />
          </TabsContent>

          <TabsContent value="content-calendar">
            <ContentCalendar />
          </TabsContent>


          <TabsContent value="schemas">
            <SchemaManager />
          </TabsContent>

          <TabsContent value="freshness">
            <ContentFreshnessMonitor />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminDashboard;
